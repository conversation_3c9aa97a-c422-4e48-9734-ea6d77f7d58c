import request from '@/utils/request'

/**
 * 文件上传API
 */

/**
 * 上传文件
 * @param {File} file - 要上传的文件
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadFile(file, onProgress) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/file/upload',
    method: 'post',
    data: formData,
    timeout: 60000, // 60秒超时
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }
  })
}

/**
 * 上传图片（专门用于图片上传的封装）
 * @param {File} file - 图片文件
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadImage(file, onProgress) {
  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp']
  if (!allowedTypes.includes(file.type)) {
    return Promise.reject(new Error('只支持上传 JPG、PNG、GIF、WebP、BMP 格式的图片'))
  }

  // 验证文件大小（2MB）
  const maxSize = 2 * 1024 * 1024
  if (file.size > maxSize) {
    return Promise.reject(new Error('图片大小不能超过 2MB'))
  }

  // 使用专门的图片上传接口
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/file/upload/image',
    method: 'post',
    data: formData,
    timeout: 60000, // 60秒超时
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }
  })
}

/**
 * 上传视频
 * @param {File} file - 视频文件
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadVideo(file, onProgress) {
  // 验证文件类型
  const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv']
  if (!allowedTypes.includes(file.type)) {
    return Promise.reject(new Error('只支持上传 MP4、AVI、MOV、WMV、FLV 格式的视频'))
  }

  // 验证文件大小（50MB）
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    return Promise.reject(new Error('视频大小不能超过 50MB'))
  }

  return uploadFile(file, onProgress)
}

/**
 * 上传音频
 * @param {File} file - 音频文件
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadAudio(file, onProgress) {
  // 验证文件类型
  const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac']
  if (!allowedTypes.includes(file.type)) {
    return Promise.reject(new Error('只支持上传 MP3、WAV、OGG、AAC、FLAC 格式的音频'))
  }

  // 验证文件大小（20MB）
  const maxSize = 20 * 1024 * 1024
  if (file.size > maxSize) {
    return Promise.reject(new Error('音频大小不能超过 20MB'))
  }

  return uploadFile(file, onProgress)
}
