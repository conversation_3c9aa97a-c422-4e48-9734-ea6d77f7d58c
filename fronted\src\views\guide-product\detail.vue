<template>
  <div class="guide-product-detail">
    <div class="page-header">
      <h2>讲解产品详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品ID">
          {{ detail.productId }}
        </el-descriptions-item>
        <el-descriptions-item label="产品标题">
          {{ detail.title }}
        </el-descriptions-item>
        <el-descriptions-item label="景区ID">
          {{ detail.scenicId }}
        </el-descriptions-item>
        <el-descriptions-item label="产品类型">
          {{ detail.type }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解时长">
          {{ detail.duration }}
        </el-descriptions-item>
        <el-descriptions-item label="产品价格">
          ¥{{ detail.price }}
        </el-descriptions-item>
        <el-descriptions-item label="背景图片" :span="2">
          <el-image
            v-if="detail.backgroundImageUrl"
            :src="detail.backgroundImageUrl"
            style="width: 300px; height: 100px"
            fit="cover"
            :preview-src-list="[detail.backgroundImageUrl]"
          />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="示例视频URL" :span="2">
          <a v-if="detail.exampleVideoUrl" :href="detail.exampleVideoUrl" target="_blank">
            {{ detail.exampleVideoUrl }}
          </a>
          <span v-else>暂无视频</span>
        </el-descriptions-item>
        <el-descriptions-item label="讲解员ID">
          {{ detail.lecturerId }}
        </el-descriptions-item>
        <el-descriptions-item label="地图URL">
          <a v-if="detail.mapUrl" :href="detail.mapUrl" target="_blank">查看地图</a>
          <span v-else>暂无地图</span>
        </el-descriptions-item>
        <el-descriptions-item label="开始收听图片" :span="2">
          <a v-if="detail.startListeningImageUrl" :href="detail.startListeningImageUrl" target="_blank">
            {{ detail.startListeningImageUrl }}
          </a>
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="产品描述" :span="2">
          <div class="content-text">{{ detail.description || '暂无描述' }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getGuideProductById } from '@/api/guideProduct'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getGuideProductById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取讲解产品详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/guide-product/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/guide-product/list')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.guide-product-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}
</style>
